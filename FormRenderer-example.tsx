import React from 'react';
import <PERSON><PERSON><PERSON>er from './FormRenderer';

// Example JSON Schema with array support
const exampleSchema = {
  type: 'object',
  properties: {
    name: {
      type: 'string',
      title: 'Name',
      description: 'Enter your full name'
    },
    age: {
      type: 'integer',
      title: 'Age',
      minimum: 0,
      maximum: 120
    },
    // Array with enum items - will render as multi-select checkboxes
    hobbies: {
      type: 'array',
      title: 'Hobbies',
      description: 'Select your hobbies',
      items: {
        type: 'string',
        enum: ['reading', 'gaming', 'sports', 'music', 'cooking', 'traveling'],
        enumNames: ['Reading', 'Gaming', 'Sports', 'Music', 'Cooking', 'Traveling']
      },
      minItems: 1,
      maxItems: 4,
      uniqueItems: true
    },
    // Array with enum items and default values
    skills: {
      type: 'array',
      title: 'Programming Skills',
      description: 'Select your programming skills',
      items: {
        type: 'string',
        enum: ['javascript', 'typescript', 'react', 'node', 'python', 'java'],
        enumNames: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'Java']
      },
      default: ['javascript', 'react'],
      minItems: 1,
      uniqueItems: true
    },
    // Array without enum - will render as comma-separated input
    tags: {
      type: 'array',
      title: 'Tags',
      description: 'Enter tags separated by commas',
      items: {
        type: 'string'
      },
      default: ['frontend', 'developer']
    },
    // Regular string field for comparison
    bio: {
      type: 'string',
      title: 'Bio',
      format: 'textarea',
      description: 'Tell us about yourself'
    }
  },
  required: ['name', 'hobbies', 'skills']
};

const ExampleForm: React.FC = () => {
  const initialData = {
    name: 'John Doe',
    age: 25,
    hobbies: ['reading', 'gaming'],
    skills: ['javascript', 'react', 'typescript'],
    tags: ['frontend', 'developer', 'react'],
    bio: 'I am a passionate developer...'
  };

  const handleSubmit = (data: any) => {
    console.log('Form submitted:', data);
    alert('Form submitted! Check console for data.');
  };

  const handleChange = () => {
    console.log('Form changed');
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">FormRenderer Array Support Example</h1>
      <FormRenderer
        schema={exampleSchema}
        initialData={initialData}
        onSubmit={handleSubmit}
        onChange={handleChange}
      />
    </div>
  );
};

export default ExampleForm;
