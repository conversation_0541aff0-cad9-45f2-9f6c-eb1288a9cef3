import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { Checkbox } from '../ui/Checkbox';
import { Select } from '../ui/Select';
import { Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface FormRendererProps {
  schema: any;
  initialData: any;
  onSubmit: (values: any) => void;
  onChange?: () => void;
  onCancel?: () => void;
  isSaving?: boolean;
}

const FormRenderer: React.FC<FormRendererProps> = ({ 
  schema, 
  initialData, 
  onSubmit,
  onChange,
  onCancel,
  isSaving = false
}) => {
  const { t } = useTranslation();
  const { register, handleSubmit, setValue, watch, formState: { errors, isDirty }, reset } = useForm({
    defaultValues: initialData,
    mode: 'onChange'
  });

  // Reset form when initialData changes
  React.useEffect(() => {
    reset(initialData);
  }, [initialData, reset]);

  React.useEffect(() => {
    const subscription = watch(() => {
      onChange?.();
    });
    return () => subscription.unsubscribe();
  }, [watch, onChange]);

  const handleCancel = () => {
    reset(initialData);
    onCancel?.();
  };

  const isFieldRequired = (fieldName: string) => {
    return schema.required?.includes(fieldName) ?? false;
  };

  const getValidationRules = (field: any, key: string) => {
    const rules: any = {};

    if (isFieldRequired(key)) {
      rules.required = t('validation.required');
    }

    if (field.type === 'string') {
      if (field.minLength !== undefined) {
        rules.minLength = {
          value: field.minLength,
          message: t('validation.minLength', { min: field.minLength })
        };
      }

      if (field.maxLength !== undefined) {
        rules.maxLength = {
          value: field.maxLength,
          message: t('validation.maxLength', { max: field.maxLength })
        };
      }

      if (field.format === 'uri') {
        rules.pattern = {
          value: /^https?:\/\/.+/,
          message: t('validation.uri')
        };
      }

      if (field.format === 'hostname') {
        rules.pattern = {
          value: /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/,
          message: t('validation.hostname')
        };
      }

      if (field.pattern) {
        rules.pattern = {
          value: new RegExp(field.pattern),
          message: field.patternError || t('validation.pattern')
        };
      }
    }

    if (field.type === 'integer' || field.type === 'number') {
      rules.valueAsNumber = true;

      if (field.minimum !== undefined) {
        rules.min = {
          value: field.minimum,
          message: t('validation.min', { min: field.minimum })
        };
      }

      if (field.maximum !== undefined) {
        rules.max = {
          value: field.maximum,
          message: t('validation.max', { max: field.maximum })
        };
      }

      if (field.multipleOf !== undefined) {
        rules.validate = {
          multipleOf: (value: number) => 
            value % field.multipleOf === 0 || 
            t('validation.multipleOf', { n: field.multipleOf })
        };
      }
    }

    if (field.enum) {
      rules.validate = {
        enum: (value: any) => 
          field.enum.includes(value) || 
          t('validation.enum')
      };
    }

    return rules;
  };

  const renderField = (key: string, field: any, path = '') => {
    const fieldPath = path ? `${path}.${key}` : key;
    const fieldProps = register(fieldPath, getValidationRules(field, key));
    const value = watch(fieldPath);
    const error = errors[fieldPath];

    switch (field.type) {
      case 'string':
        if (field.format === 'textarea') {
          return (
            <Textarea
              id={fieldPath}
              {...fieldProps}
              defaultValue={initialData[key] || field.default || ''}
              placeholder={field.placeholder || ''}
              className={error ? 'border-error' : ''}
            />
          );
        }
        if (field.enum) {
          return (
            <Select 
              id={fieldPath}
              {...fieldProps}
              value={String(value || field.default || '')}
              onChange={(e) => {
                setValue(fieldPath, e.target.value, { 
                  shouldDirty: true,
                  shouldValidate: true,
                  shouldTouch: true
                });
              }}
              className={error ? 'border-error' : ''}
            >
              <option value="" disabled>{field.placeholder || t('common.pleaseSelect')}</option>
              {field.enum.map((value: string, index: number) => (
                <option key={value} value={value}>
                  {field.enumNames?.[index] || value}
                </option>
              ))}
            </Select>
          );
        }
        return (
          <Input
            type="text"
            id={fieldPath}
            {...fieldProps}
            defaultValue={initialData[key] || field.default || ''}
            placeholder={field.placeholder || ''}
            className={error ? 'border-error' : ''}
          />
        );
      
      case 'integer':
      case 'number':
        return (
          <Input
            type="number"
            id={fieldPath}
            {...fieldProps}
            defaultValue={initialData[key] || field.default || ''}
            placeholder={field.placeholder || ''}
            min={field.minimum}
            max={field.maximum}
            step={field.multipleOf || (field.type === 'integer' ? 1 : 'any')}
            className={error ? 'border-error' : ''}
          />
        );
      
      case 'boolean':
        return (
          <Checkbox
            id={fieldPath}
            {...fieldProps}
            defaultChecked={initialData[key] ?? field.default ?? false}
          />
        );
      
      case 'object':
        return (
          <div className="space-y-4 border border-border rounded-md p-4">
            {Object.entries(field.properties).map(([subKey, subField]: [string, any]) => (
              <div key={subKey} className="space-y-2">
                <label htmlFor={`${fieldPath}.${subKey}`} className="block text-sm font-medium">
                  {subField.title || subKey}
                  {isFieldRequired(subKey) && <span className="text-error ml-1">*</span>}
                </label>
                {subField.description && (
                  <p className="text-xs text-muted-foreground mb-2">{subField.description}</p>
                )}
                {renderField(subKey, subField, fieldPath)}
                {errors[`${fieldPath}.${subKey}`] && (
                  <p className="text-sm text-error">{errors[`${fieldPath}.${subKey}`]?.message?.toString()}</p>
                )}
              </div>
            ))}
          </div>
        );
      
      default:
        return (
          <Input
            type="text"
            id={fieldPath}
            {...fieldProps}
            defaultValue={initialData[key] || field.default || ''}
            className={error ? 'border-error' : ''}
          />
        );
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {schema && Object.entries(schema.properties).map(([key, field]: [string, any]) => (
        <div key={key} className="space-y-2">
          <label htmlFor={key} className="block text-sm font-medium">
            {field.title || key}
            {isFieldRequired(key) && <span className="text-error ml-1">*</span>}
          </label>
          {field.description && (
            <p className="text-xs text-muted-foreground mb-2">{field.description}</p>
          )}
          {renderField(key, field)}
          {errors[key] && (
            <p className="text-sm text-error">{errors[key]?.message?.toString()}</p>
          )}
        </div>
      ))}
      
      <div className="flex justify-end gap-3">
        <Button 
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={!isDirty || isSaving}
          className="bg-background"
        >
          {t('common.cancel')}
        </Button>
        <Button 
          type="submit" 
          disabled={!isDirty || isSaving}
          className="flex items-center gap-2"
        >
          {isSaving && <Loader2 className="h-4 w-4 animate-spin" />}
          {isSaving ? t('common.saving') : t('common.save')}
        </Button>
      </div>
    </form>
  );
};

export default FormRenderer;
